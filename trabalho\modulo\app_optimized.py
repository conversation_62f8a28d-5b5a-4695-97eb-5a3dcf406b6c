"""
Versão otimizada do app.py com melhorias em funcionalidade, velocidade e escalabilidade
"""
import streamlit as st
import cv2
import numpy as np
import pandas as pd
from PIL import Image
import io
import base64
import os
import traceback
import asyncio
import logging
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
import json
from datetime import datetime

# Imports locais
from huggingface_service import virtual_try_on_streamlit, get_hf_service
from processamento import extrair_dados_da_imagem
from recomendacao import recomendar_roupas

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuração da página
st.set_page_config(
    page_title="VesteAI - Análise de Coloração",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

class AppConfig:
    """Configurações centralizadas da aplicação"""
    
    # Cores do tema
    COLORS = {
        'background': '#FFF5E1',
        'sidebar': '#FFDFD3',
        'primary_text': '#5D4037',
        'secondary_text': '#795548',
        'button': '#5D4037',
        'button_hover': '#4E342E'
    }
    
    # Paths
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / "data"
    SLIDES_DIR = DATA_DIR / "slides"
    IMAGES_DIR = DATA_DIR / "imagens_corpos"
    
    # Configurações de performance
    MAX_IMAGE_SIZE = (1024, 1024)
    CACHE_TTL = 3600  # 1 hora

class ImageProcessor:
    """Classe para processamento otimizado de imagens"""
    
    @staticmethod
    def pil_to_opencv(pil_image: Image.Image) -> np.ndarray:
        """Convert PIL image to OpenCV format"""
        open_cv_image = np.array(pil_image)
        return open_cv_image[:, :, ::-1].copy()
    
    @staticmethod
    def opencv_to_pil(cv_img: np.ndarray) -> Image.Image:
        """Convert OpenCV image to PIL"""
        cv_img_rgb = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
        _, buffer = cv2.imencode('.png', cv_img_rgb)
        img_buffer = io.BytesIO(buffer)
        return Image.open(img_buffer)
    
    @staticmethod
    def optimize_image_for_upload(image: Image.Image, max_size: Tuple[int, int] = (512, 512)) -> Image.Image:
        """Otimiza imagem para upload reduzindo tamanho"""
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        if image.mode != 'RGB':
            image = image.convert('RGB')
        return image
    
    @staticmethod
    def create_downloadable_image(cv_img: np.ndarray, filename: str = "analysis_result.png") -> Tuple[bytes, str]:
        """Create a downloadable image"""
        pil_img = ImageProcessor.opencv_to_pil(cv_img)
        buffer = io.BytesIO()
        pil_img.save(buffer, format='PNG')
        buffer.seek(0)
        return buffer.getvalue(), filename

class ColorAnalyzer:
    """Classe para análise de cores"""
    
    @staticmethod
    def create_color_panel(medidas: Dict[str, Any]) -> np.ndarray:
        """Create a color panel showing extracted colors"""
        painel = np.full((400, 600, 3), (211, 223, 255), dtype=np.uint8)
        y_pos = 50
        
        def draw_text_with_accents(img_cv2, texto, pos, cor=(0, 0, 0), tamanho=20):
            img_pil = Image.fromarray(cv2.cvtColor(img_cv2, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)
            try:
                fonte = ImageFont.truetype("arial.ttf", tamanho)
            except:
                fonte = ImageFont.load_default()
            
            x, y = pos
            # Contorno
            draw.text((x - 1, y - 1), texto, font=fonte, fill=cor)
            draw.text((x + 1, y + 1), texto, font=fonte, fill=cor)
            # Texto principal
            draw.text((x, y), texto, font=fonte, fill=cor)
            
            return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        # Skin tone
        if 'tom_de_pele' in medidas:
            cv2.putText(painel, "Tom de Pele:", (20, y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            cor_pele = tuple(map(int, medidas['tom_de_pele']))
            cv2.rectangle(painel, (200, y_pos - 20), (300, y_pos + 20), cor_pele, -1)
            cv2.putText(painel, f"BGR: {list(cor_pele)}", (320, y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            y_pos += 80
        
        # Hair tone
        if 'tom_de_cabelo' in medidas and not medidas.get('pouco_cabelo', True):
            cv2.putText(painel, "Tom de Cabelo:", (20, y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            cor_cabelo = tuple(map(int, medidas['tom_de_cabelo']))
            cv2.rectangle(painel, (200, y_pos - 20), (300, y_pos + 20), cor_cabelo, -1)
            cv2.putText(painel, f"BGR: {list(cor_cabelo)}", (320, y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            y_pos += 80
        
        # Eye tone
        if 'tom_de_olho' in medidas:
            cv2.putText(painel, "Tom dos Olhos:", (20, y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            cor_olho = tuple(map(int, medidas['tom_de_olho']))
            cv2.rectangle(painel, (200, y_pos - 20), (300, y_pos + 20), cor_olho, -1)
            cv2.putText(painel, f"BGR: {list(cor_olho)}", (320, y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            y_pos += 80
        
        # Classification
        if 'Classificação' in medidas:
            texto = f"Contraste: {medidas['Classificação'].capitalize()}"
            painel = draw_text_with_accents(painel, texto, (20, y_pos))
            y_pos += 40
        
        if 'Subtom' in medidas:
            cv2.putText(painel, f"Subtom: {medidas['Subtom'].capitalize()}", (20, y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        return painel
    
    @staticmethod
    def display_color_grid(cores_bgr: List[List[int]]):
        """Display colors in a grid format"""
        if not cores_bgr:
            st.warning("Nenhuma cor encontrada para exibir.")
            return
        
        cols_per_row = 5
        rows = len(cores_bgr) // cols_per_row + (1 if len(cores_bgr) % cols_per_row else 0)
        
        for row in range(rows):
            cols = st.columns(cols_per_row)
            for col_idx in range(cols_per_row):
                color_idx = row * cols_per_row + col_idx
                if color_idx < len(cores_bgr):
                    with cols[col_idx]:
                        cor_bgr = cores_bgr[color_idx]
                        cor_rgb = (cor_bgr[2], cor_bgr[1], cor_bgr[0])
                        cor_hex = f"#{cor_rgb[0]:02x}{cor_rgb[1]:02x}{cor_rgb[2]:02x}"
                        
                        st.markdown(f"""
                        <div style="
                            width: 80px; 
                            height: 80px; 
                            background-color: {cor_hex}; 
                            border: 2px solid #ddd;
                            border-radius: 8px;
                            margin: 5px auto;
                        "></div>
                        <p style="text-align: center; font-size: 10px; margin: 0;">
                            RGB: {cor_rgb}
                        </p>
                        """, unsafe_allow_html=True)

class ClothingRecommender:
    """Classe para recomendações de roupas"""
    
    @staticmethod
    def generate_recommendations(medidas: Dict[str, Any]) -> Tuple[List[List[int]], Optional[str]]:
        """Generate clothing recommendations"""
        try:
            # Try multiple paths for the CSV file
            possible_paths = [
                AppConfig.DATA_DIR / 'catalogo_roupas.csv',
                Path('catalogo_roupas.csv'),
                Path('data/catalogo_roupas.csv')
            ]
            
            csv_path = None
            for path in possible_paths:
                if path.exists():
                    csv_path = path
                    break
            
            if not csv_path:
                st.error("❌ Arquivo CSV do catálogo não encontrado.")
                return [], None
            
            catalogo = pd.read_csv(csv_path)
            catalogo.columns = catalogo.columns.str.strip().str.lower()
            
            # Recommendation logic (simplified version)
            roupas_filtradas = catalogo.copy()
            
            # Extract colors
            cores_bgr = []
            if 'cor bgr' in roupas_filtradas.columns:
                roupas_filtradas["cor bgr"] = roupas_filtradas["cor bgr"].apply(
                    lambda x: list(map(int, str(x).strip("[]").split())) if pd.notna(x) else [0, 0, 0]
                )
                
                for _, row in roupas_filtradas.iterrows():
                    if 'cor bgr' in row and isinstance(row['cor bgr'], list) and len(row['cor bgr']) == 3:
                        cores_bgr.append(row['cor bgr'])
            
            return cores_bgr, "primavera brilhante"  # Simplified
            
        except Exception as e:
            logger.error(f"Erro ao processar recomendações: {e}")
            return [], None

class VirtualTryOn:
    """Classe para virtual try-on"""
    
    @staticmethod
    async def process_virtual_try_on(person_image: Image.Image) -> Optional[str]:
        """Process virtual try-on with uploaded garment"""
        st.subheader("👗 Virtual Try-On")
        
        uploaded_garment = st.file_uploader(
            "Arraste a imagem da roupa ou envie alguma de sua escolha:",
            type=["jpg", "png", "jpeg"],
            key="garment_upload"
        )
        
        if uploaded_garment:
            try:
                garment_image = Image.open(uploaded_garment)
                
                # Otimizar imagens
                person_optimized = ImageProcessor.optimize_image_for_upload(person_image)
                garment_optimized = ImageProcessor.optimize_image_for_upload(garment_image)
                
                # Mostrar preview
                col1, col2 = st.columns(2)
                with col1:
                    st.image(person_optimized, caption="Sua foto", use_container_width=True)
                with col2:
                    st.image(garment_optimized, caption="Roupa escolhida", use_container_width=True)
                
                # Processar virtual try-on
                if st.button("🎨 Gerar Virtual Try-On", type="primary"):
                    with st.spinner("Gerando sua imagem com a roupa..."):
                        result_path = await virtual_try_on_streamlit(person_optimized, garment_optimized)
                        
                        if result_path:
                            st.success("✅ Virtual try-on concluído!")
                            st.image(result_path, caption="Resultado", use_container_width=True)
                            
                            # Botão de download
                            with open(result_path, "rb") as f:
                                st.download_button(
                                    "📥 Baixar imagem gerada",
                                    f.read(),
                                    file_name=f"virtual_tryon_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
                                    mime="image/png"
                                )
                            
                            return result_path
                        else:
                            st.error("❌ Erro na geração da imagem")
                            
            except Exception as e:
                logger.error(f"Erro no virtual try-on: {e}")
                st.error(f"Erro: {str(e)}")
        
        return None

class AppUI:
    """Classe para interface do usuário"""
    
    @staticmethod
    def load_custom_css():
        """Load custom CSS styles"""
        css = f"""
        <style>
            .stApp {{
                background-color: {AppConfig.COLORS['background']};
            }}
            .main-header, h1 {{
                color: {AppConfig.COLORS['primary_text']} !important;
                text-align: center;
            }}
            h2, h3, h4, h5, h6 {{
                color: {AppConfig.COLORS['primary_text']};
            }}
            p, .stMarkdown, .stText, .stAlert, label {{
                color: {AppConfig.COLORS['primary_text']};
            }}
            .stSidebar > div:first-child {{
                background-color: {AppConfig.COLORS['sidebar']};
            }}
            .stSidebar .stMarkdown p, .stSidebar .stText, .stSidebar label, 
            .stSidebar h1, .stSidebar h2, .stSidebar h3 {{
                color: {AppConfig.COLORS['primary_text']} !important;
            }}
            .stButton>button {{
                background-color: {AppConfig.COLORS['button']};
                color: #FFFFFF;
                border-radius: 20px;
                border: 1px solid {AppConfig.COLORS['button']};
                padding: 0.5em 1em;
                transition: background-color 0.3s ease, color 0.3s ease;
            }}
            .stButton>button:hover {{
                background-color: {AppConfig.COLORS['button_hover']};
                color: {AppConfig.COLORS['background']};
            }}
        </style>
        """
        st.markdown(css, unsafe_allow_html=True)
    
    @staticmethod
    def create_sidebar():
        """Create sidebar with instructions"""
        with st.sidebar:
            st.header("📋 Instruções")
            st.markdown("""
            **Como funciona?**
            1. **Envie sua foto:** Use o botão ao lado.
            2. **Aguarde a análise:** Nossa IA processará sua imagem.
            3. **Receba sua paleta:** Descubra sua estação e cores ideais!
            
            **Dicas para a foto:**
            - Foto de corpo inteiro
            - Iluminação natural
            - Rosto bem visível
            - Fundo neutro
            - Evite filtros ou edições
            """)
            
            st.header("ℹ️ Sobre a Análise")
            st.markdown("""
            Esta ferramenta analisa:
            - Tom de pele, cabelo e olhos
            - Contraste facial
            - Subtom (quente/frio/neutro/oliva)
            - Recomendações de cores
            - Sugestão de roupas
            """)
            
            # Cache stats
            if st.button("📊 Estatísticas do Cache"):
                service = get_hf_service()
                stats = service.get_cache_stats()
                st.json(stats)
            
            if st.button("🗑️ Limpar Cache"):
                service = get_hf_service()
                service.clear_cache()
                st.success("Cache limpo!")
            
            st.markdown("---")
            st.caption("Desenvolvido com ❤️ e IA.")

class VesteAIApp:
    """Classe principal da aplicação"""
    
    def __init__(self):
        self.config = AppConfig()
        self.image_processor = ImageProcessor()
        self.color_analyzer = ColorAnalyzer()
        self.clothing_recommender = ClothingRecommender()
        self.virtual_try_on = VirtualTryOn()
        self.ui = AppUI()
    
    def run(self):
        """Executa a aplicação principal"""
        # Load CSS
        self.ui.load_custom_css()
        
        # Header
        st.markdown('<h1 class="main-header">VesteAI</h1>', unsafe_allow_html=True)
        
        # Load slides
        self._load_slides()
        
        # Main title
        st.markdown('<h1 class="main-header">🎨 Análise de Coloração Pessoal</h1>', unsafe_allow_html=True)
        st.markdown("**Upload uma foto para análise completa das suas características de cor e estilo!**")
        
        # Sidebar
        self.ui.create_sidebar()
        
        # Main content
        uploaded_file = st.file_uploader(
            "Escolha uma imagem",
            type=['png', 'jpg', 'jpeg'],
            help="Faça upload de uma foto com boa iluminação"
        )
        
        if uploaded_file is not None:
            self._process_uploaded_image(uploaded_file)
    
    def _load_slides(self):
        """Load presentation slides"""
        try:
            slides_dir = self.config.SLIDES_DIR
            if slides_dir.exists():
                for i in range(1, 5):
                    slide_path = slides_dir / f"slide{i}.png"
                    if slide_path.exists():
                        st.image(Image.open(slide_path), use_container_width=True)
        except Exception as e:
            logger.warning(f"Erro ao carregar slides: {e}")
    
    def _process_uploaded_image(self, uploaded_file):
        """Process uploaded image"""
        try:
            # Load image
            image_bytes = uploaded_file.read()
            image = Image.open(io.BytesIO(image_bytes))
            
            col1, col2 = st.columns([1, 2])
            
            with col1:
                st.subheader("📸 Imagem Enviada")
                st.image(image, caption="Sua foto", use_container_width=True)
            
            with col2:
                st.subheader("📊 Resultados da Análise")
                
                # Process analysis
                with st.spinner("Analisando sua coloração pessoal..."):
                    cv_image = self.image_processor.pil_to_opencv(image)
                    medidas, resultado = extrair_dados_da_imagem(cv_image, image_bytes)
                    
                    # Store in session state
                    st.session_state.medidas = medidas
                    st.session_state.analysis_complete = True
                
                # Display results
                self._display_analysis_results()
            
            # Recommendations
            self._display_recommendations()
            
            # Virtual Try-On
            st.divider()
            asyncio.run(self.virtual_try_on.process_virtual_try_on(image))
            
            # Complete analysis
            with st.expander("📋 Ver Dicionário Completo de Análise"):
                st.json(st.session_state.medidas)
                
        except Exception as e:
            logger.error(f"Erro ao processar imagem: {e}")
            st.error(f"Erro na análise: {str(e)}")
            st.code(traceback.format_exc())
    
    def _display_analysis_results(self):
        """Display analysis results"""
        if st.session_state.get('analysis_complete', False) and 'medidas' in st.session_state:
            col_res1, col_res2 = st.columns(2)
            
            with col_res1:
                st.markdown("### 🧍 Medidas Corporais")
                medidas_corporais = {
                    k: v for k, v in st.session_state.medidas.items()
                    if k in ['altura_total', 'largura_ombros', 'largura_quadril', 'proporção',
                             'Tipo de corpo', 'Formato do rosto']
                }
                if medidas_corporais:
                    for key, value in medidas_corporais.items():
                        st.metric(key.replace('_', ' ').title(), value)
                else:
                    st.info("Medidas corporais não detectadas")
            
            with col_res2:
                st.markdown("### 🎨 Análise de Cores")
                analise_cores = {
                    k: v for k, v in st.session_state.medidas.items()
                    if k in ['Classificação', 'Subtom', 'Tom de pele (escala 0-10)',
                             'Tom de cabelo (escala 0-10)', 'Tom dos olhos (escala 0-10)', 'Intensidade']
                }
                if analise_cores:
                    for key, value in analise_cores.items():
                        st.metric(key, value)
                else:
                    st.info("Análise de cores não disponível")
    
    def _display_recommendations(self):
        """Display color recommendations"""
        st.divider()
        st.subheader("👗 Recomendações de Cores")
        
        with st.spinner("Buscando roupas ideais para você..."):
            cores_recomendadas, estacao = self.clothing_recommender.generate_recommendations(
                st.session_state.medidas
            )
            
            if cores_recomendadas:
                st.subheader(f"🎨 PARABÉNS! A sua estação é {estacao.capitalize()}")
                self.color_analyzer.display_color_grid(cores_recomendadas)
                
                # Download report
                try:
                    palette_data = self._create_color_report(cores_recomendadas, st.session_state.medidas)
                    st.download_button(
                        label="📥 Baixar Relatório de Cores",
                        data=palette_data,
                        file_name="color_palette_report.txt",
                        mime="text/plain"
                    )
                except Exception as e:
                    st.error(f"Erro ao criar relatório: {e}")
                
                st.write("Lembre-se: este é um guia. Sinta-se livre para experimentar!")
            else:
                st.warning("⚠️ Nenhuma roupa recomendada encontrada.")
    
    def _create_color_report(self, cores_bgr: List[List[int]], medidas: Dict[str, Any]) -> str:
        """Create downloadable color report"""
        report_buffer = io.StringIO()
        
        report_buffer.write("RELATÓRIO DE ANÁLISE DE COLORAÇÃO PESSOAL\n")
        report_buffer.write("=" * 50 + "\n\n")
        
        report_buffer.write("ANÁLISE PESSOAL:\n")
        report_buffer.write("-" * 20 + "\n")
        
        for key, value in medidas.items():
            if key in ['Classificação', 'Subtom', 'Tom de pele (escala 0-10)',
                       'Tom de cabelo (escala 0-10)', 'Tom dos olhos (escala 0-10)', 'Intensidade']:
                report_buffer.write(f"{key}: {value}\n")
        
        report_buffer.write("\nCORES RECOMENDADAS (RGB):\n")
        report_buffer.write("-" * 30 + "\n")
        
        for i, cor_bgr in enumerate(cores_bgr, 1):
            cor_rgb = (cor_bgr[2], cor_bgr[1], cor_bgr[0])
            cor_hex = f"#{cor_rgb[0]:02x}{cor_rgb[1]:02x}{cor_rgb[2]:02x}"
            report_buffer.write(f"Cor {i:2d}: RGB{cor_rgb} - HEX: {cor_hex}\n")
        
        report_buffer.write(f"\nTotal de cores recomendadas: {len(cores_bgr)}\n")
        report_buffer.write("\nRelatório gerado automaticamente pelo sistema de análise de coloração pessoal.\n")
        
        content = report_buffer.getvalue()
        report_buffer.close()
        return content

def main():
    """Função principal"""
    app = VesteAIApp()
    app.run()

if __name__ == "__main__":
    main()
