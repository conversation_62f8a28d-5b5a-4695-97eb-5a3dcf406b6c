"""
Sistema de logging avançado para a aplicação VesteAI
"""
import logging
import logging.handlers
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional
import json

class ColoredFormatter(logging.Formatter):
    """Formatter com cores para logs no terminal"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Adicionar cor baseada no nível
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class JSONFormatter(logging.Formatter):
    """Formatter para logs em formato JSON"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Adicionar informações de exceção se houver
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Adicionar campos extras se houver
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)

class PerformanceFilter(logging.Filter):
    """Filtro para logs de performance"""
    
    def filter(self, record):
        # Adicionar timestamp de performance
        record.performance_time = datetime.now().isoformat()
        return True

class LoggingConfig:
    """Configuração centralizada de logging"""
    
    def __init__(self, 
                 log_dir: str = "logs",
                 log_level: str = "INFO",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 enable_console: bool = True,
                 enable_file: bool = True,
                 enable_json: bool = False):
        
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        self.log_level = getattr(logging, log_level.upper())
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.enable_console = enable_console
        self.enable_file = enable_file
        self.enable_json = enable_json
        
        self._setup_logging()
    
    def _setup_logging(self):
        """Configura o sistema de logging"""
        # Limpar handlers existentes
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        
        # Configurar nível
        root_logger.setLevel(self.log_level)
        
        # Formatters
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        json_formatter = JSONFormatter()
        
        # Console handler
        if self.enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.log_level)
            console_handler.setFormatter(console_formatter)
            console_handler.addFilter(PerformanceFilter())
            root_logger.addHandler(console_handler)
        
        # File handlers
        if self.enable_file:
            # Log geral
            general_log_file = self.log_dir / "vesteai.log"
            general_handler = logging.handlers.RotatingFileHandler(
                general_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            general_handler.setLevel(self.log_level)
            general_handler.setFormatter(file_formatter)
            general_handler.addFilter(PerformanceFilter())
            root_logger.addHandler(general_handler)
            
            # Log de erros
            error_log_file = self.log_dir / "vesteai_errors.log"
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(file_formatter)
            error_handler.addFilter(PerformanceFilter())
            root_logger.addHandler(error_handler)
            
            # Log de performance
            perf_log_file = self.log_dir / "vesteai_performance.log"
            perf_handler = logging.handlers.RotatingFileHandler(
                perf_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            perf_handler.setLevel(logging.INFO)
            perf_handler.setFormatter(file_formatter)
            perf_handler.addFilter(PerformanceFilter())
            
            # Logger específico para performance
            perf_logger = logging.getLogger('performance')
            perf_logger.addHandler(perf_handler)
            perf_logger.setLevel(logging.INFO)
            perf_logger.propagate = False
        
        # JSON log se habilitado
        if self.enable_json:
            json_log_file = self.log_dir / "vesteai.json"
            json_handler = logging.handlers.RotatingFileHandler(
                json_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            json_handler.setLevel(self.log_level)
            json_handler.setFormatter(json_formatter)
            json_handler.addFilter(PerformanceFilter())
            root_logger.addHandler(json_handler)
        
        # Configurar loggers específicos
        self._configure_specific_loggers()
    
    def _configure_specific_loggers(self):
        """Configura loggers específicos da aplicação"""
        
        # Logger para Hugging Face
        hf_logger = logging.getLogger('huggingface')
        hf_logger.setLevel(logging.INFO)
        
        # Logger para processamento de imagens
        img_logger = logging.getLogger('image_processing')
        img_logger.setLevel(logging.INFO)
        
        # Logger para cache
        cache_logger = logging.getLogger('cache')
        cache_logger.setLevel(logging.INFO)
        
        # Logger para API
        api_logger = logging.getLogger('api')
        api_logger.setLevel(logging.INFO)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Retorna logger configurado"""
        return logging.getLogger(name)
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """Log específico para performance"""
        perf_logger = logging.getLogger('performance')
        perf_logger.info(f"Performance: {operation} took {duration:.2f}s", 
                        extra={'extra_fields': {'operation': operation, 'duration': duration, **kwargs}})
    
    def log_api_call(self, endpoint: str, method: str, status_code: int, duration: float, **kwargs):
        """Log específico para chamadas de API"""
        api_logger = logging.getLogger('api')
        api_logger.info(f"API Call: {method} {endpoint} - {status_code} ({duration:.2f}s)",
                       extra={'extra_fields': {
                           'endpoint': endpoint,
                           'method': method,
                           'status_code': status_code,
                           'duration': duration,
                           **kwargs
                       }})
    
    def log_cache_operation(self, operation: str, key: str, hit: bool = None, **kwargs):
        """Log específico para operações de cache"""
        cache_logger = logging.getLogger('cache')
        message = f"Cache {operation}: {key}"
        if hit is not None:
            message += f" - {'HIT' if hit else 'MISS'}"
        
        cache_logger.info(message, extra={'extra_fields': {
            'operation': operation,
            'key': key,
            'hit': hit,
            **kwargs
        }})

# Instância global
logging_config = LoggingConfig()

# Funções de conveniência
def get_logger(name: str) -> logging.Logger:
    """Retorna logger configurado"""
    return logging_config.get_logger(name)

def log_performance(operation: str, duration: float, **kwargs):
    """Log de performance"""
    logging_config.log_performance(operation, duration, **kwargs)

def log_api_call(endpoint: str, method: str, status_code: int, duration: float, **kwargs):
    """Log de chamada de API"""
    logging_config.log_api_call(endpoint, method, status_code, duration, **kwargs)

def log_cache_operation(operation: str, key: str, hit: bool = None, **kwargs):
    """Log de operação de cache"""
    logging_config.log_cache_operation(operation, key, hit, **kwargs)

# Decorator para logging de performance
def log_execution_time(operation_name: str = None):
    """Decorator para logar tempo de execução"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                log_performance(op_name, duration, success=True)
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                log_performance(op_name, duration, success=False, error=str(e))
                raise
        
        return wrapper
    return decorator
