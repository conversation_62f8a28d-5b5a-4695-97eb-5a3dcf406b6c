"""
Serviço otimizado para integração com Hugging Face API
"""
import os
import tempfile
import shutil
import asyncio
from typing import Optional, Tuple, Dict, Any
from PIL import Image
import streamlit as st
from gradio_client import Client
import logging
from functools import lru_cache
import hashlib
import json
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HuggingFaceService:
    """Serviço otimizado para integração com Hugging Face"""
    
    def __init__(self, model_name: str = "yisol/IDM-VTON", cache_dir: str = "cache"):
        self.model_name = model_name
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self._client = None
        self._client_lock = asyncio.Lock()
    
    @property
    async def client(self):
        """Lazy loading do cliente com thread safety"""
        if self._client is None:
            async with self._client_lock:
                if self._client is None:
                    try:
                        self._client = Client(self.model_name)
                        logger.info(f"Cliente Hugging Face inicializado: {self.model_name}")
                    except Exception as e:
                        logger.error(f"Erro ao inicializar cliente: {e}")
                        raise
        return self._client
    
    def _generate_cache_key(self, person_image: bytes, garment_image: bytes, params: Dict) -> str:
        """Gera chave única para cache baseada no conteúdo das imagens"""
        content = person_image + garment_image + json.dumps(params, sort_keys=True)
        return hashlib.md5(content).hexdigest()
    
    def _get_cached_result(self, cache_key: str) -> Optional[str]:
        """Recupera resultado do cache se existir"""
        cache_file = self.cache_dir / f"{cache_key}.png"
        if cache_file.exists():
            logger.info(f"Resultado encontrado no cache: {cache_key}")
            return str(cache_file)
        return None
    
    def _save_to_cache(self, cache_key: str, result_path: str):
        """Salva resultado no cache"""
        cache_file = self.cache_dir / f"{cache_key}.png"
        shutil.copy2(result_path, cache_file)
        logger.info(f"Resultado salvo no cache: {cache_key}")
    
    async def _optimize_image(self, image: Image.Image, max_size: Tuple[int, int] = (512, 512)) -> Image.Image:
        """Otimiza imagem para reduzir tamanho e tempo de processamento"""
        # Redimensiona mantendo proporção
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Converte para RGB se necessário
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        return image
    
    async def _save_temp_image(self, image: Image.Image, suffix: str = ".png") -> str:
        """Salva imagem em arquivo temporário de forma assíncrona"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
        temp_path = temp_file.name
        temp_file.close()
        
        # Salva de forma assíncrona
        await asyncio.get_event_loop().run_in_executor(
            None, image.save, temp_path, "PNG"
        )
        
        return temp_path
    
    async def _cleanup_temp_files(self, *file_paths: str):
        """Remove arquivos temporários de forma assíncrona"""
        for file_path in file_paths:
            if file_path and os.path.exists(file_path):
                try:
                    await asyncio.get_event_loop().run_in_executor(
                        None, os.unlink, file_path
                    )
                except Exception as e:
                    logger.warning(f"Erro ao remover arquivo temporário {file_path}: {e}")
    
    async def try_on_clothing(
        self, 
        person_image: Image.Image, 
        garment_image: Image.Image,
        garment_description: str = "Roupa enviada pelo usuário",
        denoise_steps: int = 20,  # Reduzido para melhor performance
        seed: int = 42,
        timeout: int = 300  # 5 minutos timeout
    ) -> Optional[str]:
        """
        Função principal para virtual try-on com otimizações
        
        Args:
            person_image: Imagem da pessoa
            garment_image: Imagem da roupa
            garment_description: Descrição da roupa
            denoise_steps: Número de passos de denoising
            seed: Seed para reprodutibilidade
            timeout: Timeout em segundos
            
        Returns:
            Caminho para a imagem resultante ou None se erro
        """
        temp_files = []
        
        try:
            # Otimizar imagens
            person_optimized = await self._optimize_image(person_image)
            garment_optimized = await self._optimize_image(garment_image)
            
            # Gerar chave de cache
            person_bytes = await asyncio.get_event_loop().run_in_executor(
                None, lambda: person_optimized.tobytes()
            )
            garment_bytes = await asyncio.get_event_loop().run_in_executor(
                None, lambda: garment_optimized.tobytes()
            )
            
            params = {
                "garment_description": garment_description,
                "denoise_steps": denoise_steps,
                "seed": seed
            }
            
            cache_key = self._generate_cache_key(person_bytes, garment_bytes, params)
            
            # Verificar cache
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
            
            # Salvar imagens temporárias
            person_temp_path = await self._save_temp_image(person_optimized)
            garment_temp_path = await self._save_temp_image(garment_optimized)
            temp_files.extend([person_temp_path, garment_temp_path])
            
            # Chamar API com timeout
            client = await self.client
            
            result = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: client.predict(
                        dict={
                            "background": file(person_temp_path),
                            "layers": [],
                            "composite": None
                        },
                        garm_img=file(garment_temp_path),
                        garment_des=garment_description,
                        is_checked=True,
                        is_checked_crop=False,
                        denoise_steps=denoise_steps,
                        seed=seed,
                        api_name="/tryon"
                    )
                ),
                timeout=timeout
            )
            
            output_path, masked_path = result
            
            # Salvar resultado final
            result_temp_path = await self._save_temp_image(Image.open(output_path))
            temp_files.append(result_temp_path)
            
            # Salvar no cache
            self._save_to_cache(cache_key, result_temp_path)
            
            logger.info("Virtual try-on concluído com sucesso")
            return result_temp_path
            
        except asyncio.TimeoutError:
            logger.error(f"Timeout na chamada da API após {timeout} segundos")
            st.error("⏰ Timeout na geração da imagem. Tente novamente.")
            return None
            
        except Exception as e:
            logger.error(f"Erro no virtual try-on: {e}")
            st.error(f"❌ Erro na geração da imagem: {str(e)}")
            return None
            
        finally:
            # Limpar arquivos temporários
            await self._cleanup_temp_files(*temp_files)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache"""
        cache_files = list(self.cache_dir.glob("*.png"))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        return {
            "total_files": len(cache_files),
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "cache_dir": str(self.cache_dir)
        }
    
    def clear_cache(self):
        """Limpa o cache"""
        for cache_file in self.cache_dir.glob("*.png"):
            cache_file.unlink()
        logger.info("Cache limpo com sucesso")

# Instância global do serviço
@st.cache_resource
def get_hf_service():
    """Retorna instância cached do serviço Hugging Face"""
    return HuggingFaceService()

# Função de conveniência para uso no Streamlit
async def virtual_try_on_streamlit(person_image: Image.Image, garment_image: Image.Image) -> Optional[str]:
    """
    Função de conveniência para usar no Streamlit
    """
    service = get_hf_service()
    return await service.try_on_clothing(person_image, garment_image)
