"""
Sistema de cache avançado para a aplicação VesteAI
"""
import os
import json
import hashlib
import pickle
import time
from pathlib import Path
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import logging
from functools import wraps
import threading

logger = logging.getLogger(__name__)

class CacheManager:
    """Gerenciador de cache com TTL e limpeza automática"""
    
    def __init__(self, cache_dir: str = "cache", default_ttl: int = 3600):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.default_ttl = default_ttl
        self._lock = threading.RLock()
        
        # Limpar cache expirado na inicialização
        self.cleanup_expired()
    
    def _get_cache_path(self, key: str) -> Path:
        """Obtém o caminho do arquivo de cache"""
        return self.cache_dir / f"{key}.cache"
    
    def _get_metadata_path(self, key: str) -> Path:
        """Obtém o caminho do arquivo de metadados"""
        return self.cache_dir / f"{key}.meta"
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Gera chave única baseada nos argumentos"""
        content = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(content.encode()).hexdigest()
    
    def _is_expired(self, metadata: Dict) -> bool:
        """Verifica se o cache expirou"""
        if 'expires_at' not in metadata:
            return True
        
        return datetime.now() > datetime.fromisoformat(metadata['expires_at'])
    
    def get(self, key: str) -> Optional[Any]:
        """Recupera item do cache"""
        with self._lock:
            try:
                cache_path = self._get_cache_path(key)
                metadata_path = self._get_metadata_path(key)
                
                if not cache_path.exists() or not metadata_path.exists():
                    return None
                
                # Verificar metadados
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                if self._is_expired(metadata):
                    self.delete(key)
                    return None
                
                # Carregar dados
                with open(cache_path, 'rb') as f:
                    data = pickle.load(f)
                
                logger.debug(f"Cache hit para chave: {key}")
                return data
                
            except Exception as e:
                logger.error(f"Erro ao recuperar do cache {key}: {e}")
                return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Armazena item no cache"""
        with self._lock:
            try:
                ttl = ttl or self.default_ttl
                expires_at = datetime.now() + timedelta(seconds=ttl)
                
                # Metadados
                metadata = {
                    'created_at': datetime.now().isoformat(),
                    'expires_at': expires_at.isoformat(),
                    'ttl': ttl,
                    'size': len(str(value))
                }
                
                # Salvar dados
                cache_path = self._get_cache_path(key)
                with open(cache_path, 'wb') as f:
                    pickle.dump(value, f)
                
                # Salvar metadados
                metadata_path = self._get_metadata_path(key)
                with open(metadata_path, 'w') as f:
                    json.dump(metadata, f)
                
                logger.debug(f"Item armazenado no cache: {key}")
                return True
                
            except Exception as e:
                logger.error(f"Erro ao armazenar no cache {key}: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """Remove item do cache"""
        with self._lock:
            try:
                cache_path = self._get_cache_path(key)
                metadata_path = self._get_metadata_path(key)
                
                deleted = False
                if cache_path.exists():
                    cache_path.unlink()
                    deleted = True
                
                if metadata_path.exists():
                    metadata_path.unlink()
                
                if deleted:
                    logger.debug(f"Item removido do cache: {key}")
                
                return deleted
                
            except Exception as e:
                logger.error(f"Erro ao remover do cache {key}: {e}")
                return False
    
    def clear(self) -> int:
        """Limpa todo o cache"""
        with self._lock:
            count = 0
            for file_path in self.cache_dir.glob("*.cache"):
                try:
                    file_path.unlink()
                    # Remover metadados correspondentes
                    meta_path = file_path.with_suffix('.meta')
                    if meta_path.exists():
                        meta_path.unlink()
                    count += 1
                except Exception as e:
                    logger.error(f"Erro ao remover {file_path}: {e}")
            
            logger.info(f"Cache limpo: {count} itens removidos")
            return count
    
    def cleanup_expired(self) -> int:
        """Remove itens expirados do cache"""
        with self._lock:
            count = 0
            for meta_path in self.cache_dir.glob("*.meta"):
                try:
                    with open(meta_path, 'r') as f:
                        metadata = json.load(f)
                    
                    if self._is_expired(metadata):
                        cache_path = meta_path.with_suffix('.cache')
                        if cache_path.exists():
                            cache_path.unlink()
                        meta_path.unlink()
                        count += 1
                        
                except Exception as e:
                    logger.error(f"Erro ao verificar expiração {meta_path}: {e}")
            
            if count > 0:
                logger.info(f"Cache expirado limpo: {count} itens removidos")
            
            return count
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache"""
        with self._lock:
            cache_files = list(self.cache_dir.glob("*.cache"))
            total_size = sum(f.stat().st_size for f in cache_files)
            
            # Contar itens expirados
            expired_count = 0
            for meta_path in self.cache_dir.glob("*.meta"):
                try:
                    with open(meta_path, 'r') as f:
                        metadata = json.load(f)
                    if self._is_expired(metadata):
                        expired_count += 1
                except:
                    pass
            
            return {
                'total_items': len(cache_files),
                'expired_items': expired_count,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'cache_dir': str(self.cache_dir),
                'default_ttl': self.default_ttl
            }
    
    def get_items(self) -> List[Dict[str, Any]]:
        """Retorna lista de itens no cache"""
        with self._lock:
            items = []
            for meta_path in self.cache_dir.glob("*.meta"):
                try:
                    with open(meta_path, 'r') as f:
                        metadata = json.load(f)
                    
                    cache_path = meta_path.with_suffix('.cache')
                    size = cache_path.stat().st_size if cache_path.exists() else 0
                    
                    items.append({
                        'key': meta_path.stem,
                        'created_at': metadata.get('created_at'),
                        'expires_at': metadata.get('expires_at'),
                        'ttl': metadata.get('ttl'),
                        'size_mb': round(size / (1024 * 1024), 2),
                        'expired': self._is_expired(metadata)
                    })
                except Exception as e:
                    logger.error(f"Erro ao obter item {meta_path}: {e}")
            
            return sorted(items, key=lambda x: x['created_at'], reverse=True)

# Instância global do cache
cache_manager = CacheManager()

# Decorator para cache automático
def cached(ttl: int = 3600, key_prefix: str = ""):
    """Decorator para cache automático de funções"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Gerar chave única
            cache_key = f"{key_prefix}{func.__name__}_{cache_manager._generate_key(*args, **kwargs)}"
            
            # Tentar recuperar do cache
            result = cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # Executar função e armazenar resultado
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator

# Funções de conveniência
def cache_get(key: str) -> Optional[Any]:
    """Recupera item do cache"""
    return cache_manager.get(key)

def cache_set(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """Armazena item no cache"""
    return cache_manager.set(key, value, ttl)

def cache_delete(key: str) -> bool:
    """Remove item do cache"""
    return cache_manager.delete(key)

def cache_clear() -> int:
    """Limpa todo o cache"""
    return cache_manager.clear()

def cache_stats() -> Dict[str, Any]:
    """Retorna estatísticas do cache"""
    return cache_manager.get_stats()
