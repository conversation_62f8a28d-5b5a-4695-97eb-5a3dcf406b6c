"""
Configurações centralizadas da aplicação VesteAI
"""
import os
from pathlib import Path
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass

@dataclass
class AppConfig:
    """Configurações da aplicação"""
    
    # Diretórios
    BASE_DIR: Path = Path(__file__).parent.parent
    DATA_DIR: Path = BASE_DIR / "data"
    CACHE_DIR: Path = BASE_DIR / "cache"
    LOGS_DIR: Path = BASE_DIR / "logs"
    
    # Modelos e APIs
    HUGGINGFACE_MODEL: str = "yisol/IDM-VTON"
    HUGGINGFACE_TIMEOUT: int = 300  # 5 minutos
    HUGGINGFACE_DENOISE_STEPS: int = 20
    
    # Performance
    MAX_IMAGE_SIZE: Tuple[int, int] = (1024, 1024)
    OPTIMIZED_IMAGE_SIZE: Tuple[int, int] = (512, 512)
    CACHE_TTL: int = 3600  # 1 hora
    
    # Cores do tema
    COLORS: Dict[str, str] = None
    
    # Configurações de logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    def __post_init__(self):
        """Inicialização pós-criação"""
        if self.COLORS is None:
            self.COLORS = {
                'background': '#FFF5E1',
                'sidebar': '#FFDFD3',
                'primary_text': '#5D4037',
                'secondary_text': '#795548',
                'button': '#5D4037',
                'button_hover': '#4E342E',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#F44336',
                'info': '#2196F3'
            }
        
        # Criar diretórios se não existirem
        for directory in [self.CACHE_DIR, self.LOGS_DIR]:
            directory.mkdir(exist_ok=True)

@dataclass
class ModelConfig:
    """Configurações dos modelos de IA"""
    
    # MediaPipe
    POSE_CONFIDENCE: float = 0.5
    FACE_CONFIDENCE: float = 0.5
    MAX_NUM_FACES: int = 1
    
    # Processamento de imagem
    IMAGE_QUALITY: int = 95
    RESIZE_METHOD: str = "LANCZOS"
    
    # Cache
    ENABLE_CACHE: bool = True
    CACHE_SIZE: int = 100  # Número máximo de itens no cache

@dataclass
class UIConfig:
    """Configurações da interface"""
    
    # Layout
    LAYOUT: str = "wide"
    SIDEBAR_STATE: str = "expanded"
    
    # Títulos
    PAGE_TITLE: str = "VesteAI - Análise de Coloração"
    PAGE_ICON: str = "🎨"
    
    # Grid de cores
    COLORS_PER_ROW: int = 5
    COLOR_SQUARE_SIZE: int = 80
    
    # Imagens
    IMAGE_COLUMNS: int = 4
    IMAGE_WIDTH: int = 200

# Instâncias globais
app_config = AppConfig()
model_config = ModelConfig()
ui_config = UIConfig()

# Configurações de ambiente
def get_env_config() -> Dict[str, Any]:
    """Obtém configurações do ambiente"""
    return {
        'HUGGINGFACE_API_KEY': os.getenv('HUGGINGFACE_API_KEY'),
        'DEBUG': os.getenv('DEBUG', 'False').lower() == 'true',
        'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
        'CACHE_ENABLED': os.getenv('CACHE_ENABLED', 'True').lower() == 'true'
    }

# Validação de configurações
def validate_config():
    """Valida as configurações da aplicação"""
    errors = []
    
    # Verificar se diretórios existem
    required_dirs = [app_config.DATA_DIR]
    for directory in required_dirs:
        if not directory.exists():
            errors.append(f"Diretório obrigatório não encontrado: {directory}")
    
    # Verificar configurações de modelo
    if app_config.HUGGINGFACE_TIMEOUT <= 0:
        errors.append("Timeout do Hugging Face deve ser positivo")
    
    if app_config.HUGGINGFACE_DENOISE_STEPS <= 0:
        errors.append("Número de passos de denoising deve ser positivo")
    
    if errors:
        raise ValueError(f"Erros de configuração: {'; '.join(errors)}")
    
    return True
